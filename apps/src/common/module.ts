import { Modu<PERSON> } from '@nestjs/common';
import { DynamoDBService } from './dynamodb.service';
import { S3Service } from './s3.service';
import { SalesforceService } from './salesforce.service';
import { DirectSalesforceService } from './direct-salesforce.service';
import { AuthService } from './auth.service';
import { LoggerService } from './cloudwatchService';
import { LoggerModule } from '@gus-eip/loggers';
import { SqsService } from './sqs.service';
@Module({
  providers: [
    DynamoDBService,
    S3Service,
    SalesforceService,
    DirectSalesforceService,
    AuthService,
    LoggerService,
    SqsService,
  ],
  exports: [
    DynamoDBService,
    S3Service,
    SalesforceService,
    DirectSalesforceService,
    AuthService,
    LoggerService,
    SqsService,
  ],
})
export class CommonModule {}
